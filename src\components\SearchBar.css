.search-bar-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255,255,255,0.03);
  border: 1px solid rgba(167, 139, 250, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(20px) saturate(130%);
}

.search-input {
  width: 100%;
  padding: 14px 20px;
  font-size: 1rem;
  border: 1px solid transparent;
  border-radius: 12px;
  background: transparent;
  color: #ffffff;
  outline: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(0px);
}

.search-input:focus {
  border-color: rgba(167, 139, 250, 0.35);
  box-shadow: 0 0 0 3px rgba(167,139,250,0.12);
}

.search-input::placeholder {
  color: #64748b;
}

.search-icon {
  position: absolute;
  right: 16px;
  width: 20px;
  height: 20px;
  color: #64748b;
  pointer-events: none;
  transition: color 0.2s ease;
}

.search-icon svg {
  width: 100%;
  height: 100%;
}

.search-input:focus + .search-icon {
  color: #8b5cf6;
}

.search-results-info {
  font-size: 0.95rem;
  color: #94a3b8;
  text-align: center;
  padding: 8px;
  background: rgba(255,255,255,0.03);
  border-radius: 8px;
  border: 1px solid rgba(167, 139, 250, 0.2);
  backdrop-filter: blur(18px) saturate(130%);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .search-bar-container {
    gap: 8px;
    margin-bottom: 20px;
  }

  .search-input {
    padding: 12px 16px;
    font-size: 0.95rem;
  }
  
  .search-icon {
    right: 14px;
    width: 18px;
    height: 18px;
  }
  
  .search-results-info {
    font-size: 0.9rem;
    padding: 6px;
  }
}
