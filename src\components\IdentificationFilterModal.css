.id-modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.id-modal-content {
  background: linear-gradient(135deg, rgba(30, 24, 40, 0.62) 0%, rgba(18, 18, 26, 0.64) 100%);
  border-radius: 8px;
  padding: 16px;
  width: min(960px, 92vw);
  max-height: 90vh;
  overflow-y: auto;
  backdrop-filter: blur(5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(167, 139, 250, 0.3);
}

.modal-header h2 {
  font-size: 0.9em;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
.selected-ids {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.empty-state {
  color: #64748b;
  font-style: italic;
  background: var(--glass-bg);
  border: 1px dashed var(--glass-border);
  padding: 12px;
  border-radius: 8px;
}

.selected-id-row {
  display: grid;
  grid-template-columns: 1.5fr auto 1fr 1fr auto;
  gap: 8px;
  align-items: center;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  padding: 8px;
  border-radius: 8px;
}

.selected-id-name {
  font-size: 0.9em;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.id-operator, .id-value {
  background: #0f0f12;
  border: 1px solid var(--glass-border);
  color: #e5e7eb;
  border-radius: 6px;
  padding: 6px 8px;
}

.id-value {
  width: 100%;
}

.remove-chip {
  background: rgba(239, 68, 68, 0.18);
  border: 1px solid rgba(239, 68, 68, 0.35);
  color: #fecaca;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.remove-chip:hover {
  transform: translateY(-1px);
}

.id-groups {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.id-group {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
}

.group-header {
  width: 100%;
  background: transparent;
  border: none;
  color: #cbd5e1;
  padding: 10px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.group-title {
  font-size: 0.9em;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.group-toggle {
  color: #64748b;
}

.id-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 8px;
  padding: 10px;
}

.id-button {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: #94a3b8;
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.id-button:hover {
  background: var(--glass-bg-2);
  border-color: rgba(167, 139, 250, 0.5);
  transform: translateY(-1px);
}

.id-button.active {
  background: linear-gradient(180deg, var(--glass-bg-2), var(--glass-bg));
  border-color: rgba(167, 139, 250, 0.55);
  color: #c4b5fd;
  box-shadow: 0 0 0 1px rgba(139, 92, 246, 0.25) inset, 0 0 14px rgba(139, 92, 246, 0.12);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 10px;
}

.done-button {
  padding: 8px 12px;
  background: rgba(139, 92, 246, 0.25);
  color: #e9d5ff;
  border: 1px solid rgba(167, 139, 250, 0.5);
  border-radius: 8px;
  cursor: pointer;
}

.done-button:hover {
  transform: translateY(-1px);
}

