.app {
  display: flex;
  min-height: 100vh;
  background: radial-gradient(1200px 600px at 20% -10%, #221430 10%, transparent 50%),
              radial-gradient(1000px 500px at 120% 10%, #1a0f28 10%, transparent 50%),
              linear-gradient(135deg, #0b0b13 0%, #12121a 100%);
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Spotlight + subtle grain/particles */
.app::before {
  content: "";
  position: fixed;
  top: -20vh;
  right: -20vw;
  width: 1200px;
  height: 1200px;
  pointer-events: none;
  background: radial-gradient(closest-side, rgba(255,255,255,0.1), rgba(139,92,246,0.08) 35%, transparent 70%);
  filter: blur(40px);
  opacity: 0.6;
  z-index: 0;
}

.app::after {
  content: "";
  position: fixed;
  inset: 0;
  pointer-events: none;
  background-image:
    radial-gradient(rgba(255,255,255,0.06) 1px, transparent 1px),
    radial-gradient(rgba(255,255,255,0.03) 1px, transparent 1px);
  background-size: 3px 3px, 4px 4px;
  background-position: 0 0, 10px 10px;
  opacity: 0.25;
  z-index: 0;
}

.main-content {
  flex: 1;
  padding: 0px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
  overflow-x: hidden;
  background: linear-gradient(180deg, rgba(255,255,255,0.02) 0%, rgba(255,255,255,0) 100%);
  backdrop-filter: blur(24px) saturate(120%);
  position: relative;
  z-index: 1;
}

.main-content.with-sidebar {
  margin-left: 320px;
}

.main-content.full-width {
  margin-left: 50px;
}

.app-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 0;
  border-bottom: 1px solid rgba(255,255,255,0.08);
  background: linear-gradient(180deg, rgba(255,255,255,0.04) 0%, rgba(255,255,255,0) 100%);
}

.app-header h1 {
  font-size: 3em;
  margin: 0 0 12px 0;
  background: linear-gradient(45deg, #a78bfa, #c084fc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 24px rgba(139, 92, 246, 0.35);
  letter-spacing: -0.02em;
  font-weight: 800;
}

.app-header p {
  font-size: 1.2em;
  color: #a3b0c2;
  margin: 0;
  font-weight: 400;
  line-height: 1.6;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #0c0c14 0%, #151524 100%);
  color: #ffffff;
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 3px solid rgba(139, 92, 246, 0.2);
  border-top: 3px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  margin-bottom: 32px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container h2,
.error-container h2 {
  color: #8b5cf6;
  margin-bottom: 20px;
  font-size: 1.8em;
  font-weight: 700;
}

.loading-container p,
.error-container p {
  color: #94a3b8;
  font-size: 1.1em;
  margin-bottom: 32px;
  max-width: 500px;
  line-height: 1.6;
}

.error-container button {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: #ffffff;
  padding: 14px 28px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1em;
  font-weight: 600;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px -1px rgba(0,0,0,0.2);
}

.error-container button:hover {
  background: var(--glass-bg-2);
  border-color: var(--glass-border-hover);
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(0,0,0,0.3);
}

/* Warning banner for mock data */
.warning-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(234, 179, 8, 0.12);
  color: #fde68a;
  border-bottom: 1px solid rgba(234, 179, 8, 0.2);
  backdrop-filter: blur(8px);
  z-index: 2;
}

.retry-btn {
  background: rgba(234, 179, 8, 0.18);
  color: #fde68a;
  border: 1px solid rgba(234, 179, 8, 0.35);
  padding: 6px 10px;
  border-radius: 6px;
  cursor: pointer;
}

.retry-btn:hover {
  background: rgba(234, 179, 8, 0.28);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .main-content.with-sidebar {
    margin-left: 0;
  }

  .main-content.full-width {
    margin-left: 40px;
  }

  .main-content {
    padding: 20px;
  }

  .app-header {
    padding: 32px 0;
    margin-bottom: 32px;
  }

  .app-header h1 {
    font-size: 2.5em;
  }

  .app-header p {
    font-size: 1.1em;
    padding: 0 20px;
  }
}
