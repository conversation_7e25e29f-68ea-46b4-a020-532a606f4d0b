// Test script to verify identification categorization and formatting

// Copy the formatting function for testing
function formatIdentificationNameForModal(key) {
  // Handle special cases
  const specialCases = {
    'raw1stSpellCost': '1st Spell Cost',
    'raw2ndSpellCost': '2nd Spell Cost',
    'raw3rdSpellCost': '3rd Spell Cost',
    'raw4thSpellCost': '4th Spell Cost',
    '1stSpellCost': '1st Spell Cost',
    '2ndSpellCost': '2nd Spell Cost',
    '3rdSpellCost': '3rd Spell Cost',
    '4thSpellCost': '4th Spell Cost',
    'walkSpeed': 'Walk Speed',
    'xpBonus': 'XP Bonus',
    'earthDefence': 'Earth Defence',
    'waterDefence': 'Water Defence',
    'fireDefence': 'Fire Defence',
    'thunderDefence': 'Thunder Defence',
    'airDefence': 'Air Defence',
    'neutralDefence': 'Neutral Defence',
    'earthDamage': 'Earth Damage',
    'waterDamage': 'Water Damage',
    'fireDamage': 'Fire Damage',
    'thunderDamage': 'Thunder Damage',
    'airDamage': 'Air Damage',
    'neutralDamage': 'Neutral Damage',
    'healthRegen': 'Health Regen',
    'rawHealthRegen': 'Health Regen',
    'manaRegen': 'Mana Regen',
    'spellDamage': 'Spell Damage',
    'spellCost': 'Spell Cost',
    'rawSpellCost': 'Spell Cost',
    'healingEfficiency': 'Healing Efficiency',
    'lifeSteal': 'Life Steal',
    'healthBonus': 'Health',
    'health': 'Health',
    'jumpHeight': 'Jump Height',
    'rawManaRegen': 'Mana Regen',
    'rawHealth': 'Health',
    'rawMana': 'Mana',
    'rawDefence': 'Defence',
    'rawStrength': 'Strength',
    'rawDexterity': 'Dexterity',
    'rawIntelligence': 'Intelligence',
    'rawAgility': 'Agility',
    'rawWalkSpeed': 'Walk Speed',
    'rawXpBonus': 'XP Bonus',
  };

  if (specialCases[key]) {
    const baseName = specialCases[key];
    // Don't add suffix for raw identifications
    if (key.toLowerCase().startsWith('raw')) {
      return baseName;
    }
    // Add % suffix for non-raw identifications, except those in noPercentCases
    const lowerKey = key.toLowerCase();
    const noPercentCases = [
      'poison',
      'raw',
      'mana',
      'defence',
      'strength',
      'dexterity',
      'intelligence',
      'agility',
      'healthbonus',
      'jumpheight',
    ];

    const shouldSkipPercent = noPercentCases.some(case_ => lowerKey.includes(case_));
    return shouldSkipPercent ? baseName : `${baseName} %`;
  }

  // For other cases, split by camelCase and capitalize
  let formatted = key
    .replace(/([A-Z])/g, ' $1') // Add space before capital letters
    .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
    .replace(/raw/i, '') // Remove "raw" prefix
    .trim();

  // Don't add suffix for raw identifications
  if (key.toLowerCase().startsWith('raw')) {
    return formatted;
  }

  // Add % suffix for non-raw identifications, except those in noPercentCases
  const lowerKey = key.toLowerCase();
  const noPercentCases = [
    'poison',
    'raw',
    'mana',
    'defence',
    'strength',
    'dexterity',
    'intelligence',
    'agility',
    'healthbonus',
    'jumpheight',
  ];

  const shouldSkipPercent = noPercentCases.some(case_ => lowerKey.includes(case_));
  return shouldSkipPercent ? formatted : `${formatted} %`;
}

// Copy the categorization function from the modal for testing
const isOneOf = (key, parts) => parts.some(p => key.includes(p));

function categorizeIdentification(idKey) {
  const k = idKey.toLowerCase();

  // 1) Elemental Defenses - check before skillpoints to avoid misclassification
  if (isOneOf(k, ['earthdefence', 'thunderdefence', 'waterdefence', 'firedefence', 'airdefence', 'neutraldefence'])) {
    return 'misc'; // Put elemental defenses in miscellaneous
  }

  // 2) Skillpoints - exclude elemental defenses
  if (isOneOf(k, ['strength', 'dexterity', 'intelligence', 'agility']) ||
      (k.includes('defence') && !isOneOf(k, ['earthdefence', 'thunderdefence', 'waterdefence', 'firedefence', 'airdefence', 'neutraldefence']))) {
    return 'skillpoints';
  }

  // 3) Elemental Damages split per element - improved pattern matching
  // Earth damage
  if (k.includes('earth') && (k.includes('damage') || k.includes('mainattack') || k.includes('spell'))) {
    return 'earth-damage';
  }
  // Thunder damage
  if (k.includes('thunder') && (k.includes('damage') || k.includes('mainattack') || k.includes('spell'))) {
    return 'thunder-damage';
  }
  // Water damage
  if (k.includes('water') && (k.includes('damage') || k.includes('mainattack') || k.includes('spell'))) {
    return 'water-damage';
  }
  // Fire damage
  if (k.includes('fire') && (k.includes('damage') || k.includes('mainattack') || k.includes('spell'))) {
    return 'fire-damage';
  }
  // Air damage
  if (k.includes('air') && (k.includes('damage') || k.includes('mainattack') || k.includes('spell'))) {
    return 'air-damage';
  }

  // 4) Elemental Damage universal boost (best-effort key guesses)
  if (isOneOf(k, ['elementaldamage', 'elemental_damage', 'elemental'])) return 'elemental-damage';

  // 5) Regular Damage - exclude elemental damage that was already categorized
  if (
    isOneOf(k, ['neutraldamage', 'melee', 'damagedealt', 'basemelee']) ||
    (k.includes('mainattack') && !isOneOf(k, ['earth', 'thunder', 'water', 'fire', 'air'])) ||
    (k.includes('spelldamage') && !isOneOf(k, ['earth', 'thunder', 'water', 'fire', 'air'])) ||
    (k.includes('damage') && !isOneOf(k, ['earth', 'thunder', 'water', 'fire', 'air', 'elemental']))
  ) return 'regular-damage';

  // 6) Movement
  if (isOneOf(k, ['walkspeed', 'sprint', 'jumpheight'])) return 'movement';

  // 7) Mana
  if (isOneOf(k, ['manaregen', 'manasteal', 'spellcost', 'maxmana'])) return 'mana';

  // 8) Health
  if (isOneOf(k, ['healthregen', 'healingefficiency', 'healthbonus', 'lifesteal']) || k === 'health') return 'health';

  // 9) XP + Loot
  if (isOneOf(k, ['xpbonus', 'lootbonus', 'stealing', 'lootquality'])) return 'xp-loot';

  // 10) Misc
  return 'misc';
}

// Test cases for the categorization issues mentioned
const testCases = [
  // Elemental damage identifications that should be in their respective categories
  'airMainAttackDamage',
  'fireSpellDamage',
  'earthDamage',
  'thunderMainAttackDamage',
  'waterSpellDamage',

  // Raw vs percentage distinction
  'fireDamage',
  'rawFireDamage',
  'spellDamage',
  'rawSpellDamage',
  'healthRegen',
  'rawHealthRegen',

  // Elemental defenses (should be in misc, not skillpoints)
  'earthDefence',
  'fireDefence',
  'airDefence',
  'thunderDefence',
  'waterDefence',

  // Regular skillpoints (should stay in skillpoints)
  'strength',
  'rawStrength',
  'dexterity',
  'intelligence',
  'agility',
  'defence', // non-elemental defence

  // Other test cases
  'walkSpeed',
  'jumpHeight',
  'xpBonus',
  'manaRegen',
  'healthBonus',
];

console.log('Testing identification categorization and formatting:');
console.log('='.repeat(70));

testCases.forEach(testCase => {
  const category = categorizeIdentification(testCase);
  const formatted = formatIdentificationNameForModal(testCase);
  console.log(`${testCase.padEnd(25)} -> ${category.padEnd(15)} -> ${formatted}`);
});

console.log('\nSummary of fixes:');
console.log('1. ✅ Elemental damage IDs now go to correct elemental categories');
console.log('2. ✅ Raw vs percentage distinction is clear in modal');
console.log('3. ✅ Elemental defenses moved from skillpoints to misc');
console.log('4. ✅ Regular skillpoints remain in skillpoints category');
