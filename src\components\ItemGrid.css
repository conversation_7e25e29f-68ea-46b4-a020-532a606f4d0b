.item-grid-container {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 20px;
}

.grid-controls {
  background: rgba(255,255,255,0.03);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid rgba(167, 139, 250, 0.2);
  backdrop-filter: blur(20px) saturate(130%);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.sort-controls span {
  color: #94a3b8;
  font-weight: 500;
  font-size: 0.95em;
}

.sort-button {
  background: rgba(255,255,255,0.03);
  border: 1px solid rgba(167, 139, 250, 0.2);
  color: #cbd5e1;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 300ms ease;
  display: flex;
  align-items: center;
  gap: 6px;
  outline: none;
  position: relative;
  overflow: hidden;
}

.sort-button:hover {
  background: rgba(255,255,255,0.05);
  border-color: rgba(167, 139, 250, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 0 0 1px rgba(167, 139, 250, 0.18) inset, 0 0 12px rgba(139, 92, 246, 0.12);
}

.sort-button:focus,
.sort-button:focus-visible {
  background: rgba(139, 92, 246, 0.2);
  box-shadow: none;
  filter: drop-shadow(0 0 20px rgba(168, 85, 247, 0.65))
          drop-shadow(0 0 40px rgba(147, 51, 234, 0.45))
          drop-shadow(0 0 64px rgba(124, 58, 237, 0.32));
}

.sort-button.active {
  background: rgba(255,255,255,0.06);
  color: #e5e7eb;
  border-color: rgba(167, 139, 250, 0.4);
  box-shadow: 0 0 0 1px rgba(255,255,255,0.06) inset;
}

/* removed animated layers for simplicity */

.sort-direction {
  font-weight: 600;
}

.item-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
  padding: 4px;
}

.no-items {
  text-align: center;
  padding: 80px 32px;
  color: #ffffff;
  background: rgba(255,255,255,0.03);
  border-radius: 12px;
  border: 1px solid rgba(167, 139, 250, 0.2);
  backdrop-filter: blur(12px);
}

.no-items h2 {
  color: #ef4444;
  margin-bottom: 16px;
  font-size: 1.8em;
  font-weight: 700;
}

.no-items p {
  color: #94a3b8;
  font-size: 1.1em;
  line-height: 1.6;
}

.load-more-trigger {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.loading-more {
  color: #94a3b8;
  font-size: 0.95em;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.page-info {
  text-align: center;
  color: #94a3b8;
  font-size: 0.95em;
  margin-bottom: 40px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .item-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
  
  .grid-controls {
    padding: 16px;
  }
  
  .sort-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .sort-controls span {
    margin-bottom: 4px;
  }
  
  .sort-button {
    padding: 6px 12px;
    font-size: 0.85em;
  }
}

@media (max-width: 480px) {
  .item-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
